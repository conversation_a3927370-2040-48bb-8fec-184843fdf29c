# Script de Configuração VPN para Windows - VERSÃO VPN INTERNA
# Conecta à VPN apenas para obter IP interno, SEM redirecionar tráfego de internet
# Compatível com diferentes versões do OpenVPN Windows
# Autor: Sistema VPN EVO-EDEN
# Data: 2025-08-20
#
# IMPORTANTE: Esta configuração NÃO redireciona o tráfego de internet pela VPN
# O Windows mantém sua conexão normal de internet e apenas obtém um IP VPN interno

param(
    [Parameter(Mandatory=$false)]
    [string]$ClientName,

    [Parameter(Mandatory=$false)]
    [string]$Password
)

# Configuracoes
$VPN_SERVER = "vpn.evo-eden.site"
$REQUIRED_PASSWORD = "VPNnbr5410!"

Write-Host "=== CONFIGURACAO VPN INTERNA PARA WINDOWS ===" -ForegroundColor Cyan
Write-Host "IMPORTANTE: Esta configuracao NAO redireciona trafego de internet" -ForegroundColor Yellow
Write-Host "A VPN sera usada apenas para obter IP interno na rede VPN" -ForegroundColor Yellow
Write-Host ""

# Solicitar dados se nao fornecidos
if (-not $ClientName) {
    $ClientName = Read-Host "Digite o nome do cliente (ex: ETEN-ANJO-DA-GUARDA-MAURITI)"
    if (-not $ClientName) {
        Write-Host "[ERRO] Nome do cliente obrigatorio" -ForegroundColor Red
        exit 1
    }
}

# Validar nome
if ($ClientName -notmatch '^[a-zA-Z0-9_-]+$') {
    Write-Host "[ERRO] Nome deve conter apenas letras, numeros, _ e -" -ForegroundColor Red
    exit 1
}

# Verificar senha
if (-not $Password) {
    $securePassword = Read-Host "Digite a senha de acesso" -AsSecureString
    $Password = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($securePassword))
}

if ($Password -ne $REQUIRED_PASSWORD) {
    Write-Host "[ERRO] Senha incorreta!" -ForegroundColor Red
    exit 1
}

Write-Host "[OK] Senha verificada" -ForegroundColor Green

# Verificar administrador
$currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
$principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
$isAdmin = $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "[ERRO] Execute como Administrador!" -ForegroundColor Red
    Write-Host "Clique com botao direito no PowerShell e selecione 'Executar como administrador'" -ForegroundColor Yellow
    exit 1
}

Write-Host "[OK] Executando como Administrador" -ForegroundColor Green

# Funcao para detectar versao do OpenVPN
function Detect-OpenVPNVersion {
    $openVPNPath = "${env:ProgramFiles}\OpenVPN\bin\openvpn.exe"

    if (Test-Path $openVPNPath) {
        try {
            $versionOutput = & $openVPNPath --version 2>&1 | Select-Object -First 1
            if ($versionOutput -match '(\d+\.\d+\.\d+)') {
                $version = $matches[1]
                Write-Host "[INFO] OpenVPN versao detectada: $version" -ForegroundColor Cyan

                # Verificar se e versao legacy (2.4 ou anterior)
                $versionParts = $version.Split('.')
                $majorMinor = "$($versionParts[0]).$($versionParts[1])"

                if ($majorMinor -eq "2.4" -or $majorMinor -eq "2.3" -or $majorMinor -eq "2.2") {
                    Write-Host "[AVISO] OpenVPN $version - usando configuracao compativel (sem data-ciphers)" -ForegroundColor Yellow
                    return "legacy"
                } else {
                    Write-Host "[INFO] OpenVPN $version - suporte completo a configuracoes modernas" -ForegroundColor Green
                    return "modern"
                }
            }
        } catch {
            Write-Host "[AVISO] Nao foi possivel detectar versao do OpenVPN - assumindo versao legacy" -ForegroundColor Yellow
            return "legacy"
        }
    } else {
        Write-Host "[AVISO] OpenVPN nao encontrado - sera necessario instalar" -ForegroundColor Yellow
        return "not_installed"
    }

    return "legacy"
}

# Funcao para verificar configuracoes VPN existentes
function Check-ExistingVPNConfigs {
    Write-Host "[PROGRESSO] Verificando configuracoes VPN existentes..." -ForegroundColor Blue

    $configPaths = @(
        "${env:ProgramFiles}\OpenVPN\config",
        "$env:USERPROFILE\OpenVPN\config",
        "$scriptDir"
    )

    $existingConfigs = @()
    $evoEdenConfigs = @()

    foreach ($path in $configPaths) {
        if (Test-Path $path) {
            $ovpnFiles = Get-ChildItem -Path $path -Filter "*.ovpn" -ErrorAction SilentlyContinue
            foreach ($file in $ovpnFiles) {
                $configFile = "$path\$($file.Name)"
                $existingConfigs += $configFile

                # Verificar se é uma configuração da nossa rede VPN (10.12.0.X)
                try {
                    $content = Get-Content $configFile -Raw -ErrorAction SilentlyContinue
                    if ($content -match "10\.12\.0\." -or $content -match "evo-eden" -or $content -match "vpn\.evo-eden\.site") {
                        $evoEdenConfigs += $configFile
                    }
                } catch {
                    # Se não conseguir ler o arquivo, ignorar
                }
            }
        }
    }

    if ($evoEdenConfigs.Count -gt 0) {
        Write-Host "[AVISO] Configuracoes VPN EVO-EDEN (10.12.0.X) encontradas:" -ForegroundColor Yellow
        foreach ($config in $evoEdenConfigs) {
            Write-Host "  • $config" -ForegroundColor White
        }
        Write-Host ""

        $response = Read-Host "Deseja remover apenas as configuracoes VPN EVO-EDEN (10.12.0.X)? [s/N]"
        if ($response -eq "s" -or $response -eq "S") {
            Clean-ExistingVPNConfigs $evoEdenConfigs
        } else {
            Write-Host "[INFO] Mantendo configuracoes VPN EVO-EDEN existentes." -ForegroundColor Cyan
        }
    } elseif ($existingConfigs.Count -gt 0) {
        Write-Host "[INFO] Encontradas configuracoes de outras VPNs (nao EVO-EDEN). Elas serao mantidas." -ForegroundColor Cyan
        foreach ($config in $existingConfigs) {
            if ($config -notin $evoEdenConfigs) {
                Write-Host "  • Mantendo: $config" -ForegroundColor White
            }
        }
    } else {
        Write-Host "[OK] Nenhuma configuracao VPN existente encontrada" -ForegroundColor Green
    }
}

# Funcao para limpar configuracoes VPN da rede EVO-EDEN (10.12.0.X)
function Clean-ExistingVPNConfigs($configs) {
    Write-Host "[PROGRESSO] Removendo configuracoes VPN EVO-EDEN (10.12.0.X)..." -ForegroundColor Blue

    foreach ($config in $configs) {
        try {
            # Verificar novamente se é realmente da nossa rede antes de remover
            $content = Get-Content $config -Raw -ErrorAction SilentlyContinue
            if ($content -match "10\.12\.0\." -or $content -match "evo-eden" -or $content -match "vpn\.evo-eden\.site") {
                Remove-Item $config -Force
                Write-Host "[OK] Removido configuracao EVO-EDEN: $config" -ForegroundColor Green
            } else {
                Write-Host "[INFO] Mantendo configuracao de outra VPN: $config" -ForegroundColor Cyan
            }
        } catch {
            Write-Host "[AVISO] Nao foi possivel processar: $config" -ForegroundColor Yellow
        }
    }

    Write-Host "[OK] Limpeza de configuracoes EVO-EDEN concluida" -ForegroundColor Green
}

# Funcao para teste avancado de conectividade
function Test-VPNConnectivity {
    Write-Host "[PROGRESSO] Testando conectividade avancada..." -ForegroundColor Blue

    # Teste 1: Resolucao DNS
    Write-Host "[INFO] Testando resolucao DNS para vpn.evo-eden.site..." -ForegroundColor Cyan
    try {
        $dnsResult = Resolve-DnsName -Name "vpn.evo-eden.site" -ErrorAction Stop
        $resolvedIP = $dnsResult[0].IPAddress
        Write-Host "[OK] DNS resolvido: $resolvedIP" -ForegroundColor Green
    } catch {
        Write-Host "[ERRO] Falha na resolucao DNS: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }

    # Teste 2: Ping basico
    Write-Host "[INFO] Testando conectividade basica (ping)..." -ForegroundColor Cyan
    try {
        $pingResult = Test-Connection -ComputerName "vpn.evo-eden.site" -Count 2 -Quiet
        if ($pingResult) {
            Write-Host "[OK] Ping para servidor bem-sucedido" -ForegroundColor Green
        } else {
            Write-Host "[ERRO] Ping para servidor falhou" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "[ERRO] Erro no teste de ping: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }

    # Teste 3: API VPN
    Write-Host "[INFO] Testando conectividade com API VPN..." -ForegroundColor Cyan
    try {
        $healthResponse = Invoke-RestMethod -Uri "http://$VPN_SERVER`:8080/api/health" -UseBasicParsing -TimeoutSec 15
        if ($healthResponse.success) {
            Write-Host "[OK] API VPN respondendo: $($healthResponse.message)" -ForegroundColor Green
        } else {
            Write-Host "[AVISO] API VPN respondeu com erro" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "[AVISO] API VPN nao acessivel: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "[INFO] Continuando mesmo assim - pode ser problema temporario" -ForegroundColor Cyan
    }

    # Teste 4: Porta UDP 1194
    Write-Host "[INFO] Testando conectividade UDP na porta 1194..." -ForegroundColor Cyan
    try {
        $udpClient = New-Object System.Net.Sockets.UdpClient
        $udpClient.Connect("vpn.evo-eden.site", 1194)
        $udpClient.Close()
        Write-Host "[OK] Conectividade UDP na porta 1194 OK" -ForegroundColor Green
    } catch {
        Write-Host "[AVISO] Teste UDP na porta 1194 falhou (pode ser normal para OpenVPN)" -ForegroundColor Yellow
    }

    Write-Host "[OK] Conectividade basica com servidor verificada" -ForegroundColor Green
    return $true
}

# Funcao para corrigir configuracao para compatibilidade
function Fix-ConfigCompatibility($configPath, $openVPNVersion) {
    Write-Host "[PROGRESSO] Aplicando correcoes de compatibilidade..." -ForegroundColor Blue

    if ($openVPNVersion -eq "legacy") {
        Write-Host "[INFO] Aplicando correcoes para OpenVPN 2.4.x..." -ForegroundColor Cyan

        # Ler conteudo do arquivo
        $content = Get-Content $configPath -Raw

        # Remover opcoes incompativeis com OpenVPN 2.4.x
        $content = $content -replace "(?m)^data-ciphers.*`r?`n", ""
        $content = $content -replace "(?m)^data-ciphers-fallback.*`r?`n", ""
        $content = $content -replace "(?m)^tls-ciphersuites.*`r?`n", ""

        # Garantir que ha configuracao de cipher compativel
        if ($content -notmatch "(?m)^cipher\s") {
            $content += "`r`n`r`n# Configuracao compativel com OpenVPN 2.4.x`r`n"
            $content += "cipher AES-256-GCM`r`n"
            $content += "auth SHA256`r`n"
        }

        # Corrigir key-direction se existir (cliente deve usar 1, servidor usa 0)
        $content = $content -replace "key-direction 0", "key-direction 1"

        # Adicionar configuracoes OBRIGATORIAS para VPN interna
        $content += "`r`n# === CONFIGURACOES VPN INTERNA OBRIGATORIAS ===`r`n"
        $content += "# IMPORTANTE: Configuracao para VPN interna apenas (sem redirecionamento de internet)`r`n"

        # SEMPRE adicionar estas configuracoes (mesmo se ja existirem, serao removidas duplicatas depois)
        $content += "route-nopull`r`n"
        $content += "script-security 2`r`n"
        $content += "connect-retry-max 3`r`n"
        $content += "`r`n# Configuracoes anti-replay (evita conflitos de timestamp)`r`n"
        $content += "mute-replay-warnings`r`n"
        $content += "replay-window 512 60`r`n"
        $content += "`r`n# Configuracoes de timeout para servidor`r`n"
        $content += "server-poll-timeout 30`r`n"
        $content += "ping 10`r`n"
        $content += "ping-restart 60`r`n"

        $content += "`r`n# Comentario explicativo`r`n"
        $content += "# Esta configuracao conecta a VPN apenas para obter IP interno`r`n"
        $content += "# O trafego de internet continua pela conexao normal do Windows`r`n"
        $content += "# Configuracoes anti-replay evitam erros ao recriar certificados`r`n"

        # Salvar arquivo corrigido
        Set-Content -Path $configPath -Value $content -Encoding UTF8

        Write-Host "[OK] Configuracao corrigida para OpenVPN 2.4.x e VPN interna" -ForegroundColor Green
    } else {
        # Para versoes modernas, ler conteudo atual e corrigir
        $currentContent = Get-Content -Path $configPath -Raw

        # Corrigir key-direction se existir (cliente deve usar 1, servidor usa 0)
        $currentContent = $currentContent -replace "key-direction 0", "key-direction 1"

        # Adicionar configuracoes OBRIGATORIAS para VPN interna
        $internalConfig = "`r`n`r`n# === CONFIGURACOES VPN INTERNA OBRIGATORIAS ===`r`n"
        $internalConfig += "# IMPORTANTE: Configuracao para VPN interna apenas (sem redirecionamento de internet)`r`n"

        # SEMPRE adicionar estas configuracoes (duplicatas serao removidas depois)
        $internalConfig += "route-nopull`r`n"
        $internalConfig += "script-security 2`r`n"
        $internalConfig += "connect-retry-max 3`r`n"
        $internalConfig += "`r`n# Configuracoes anti-replay (evita conflitos de timestamp)`r`n"
        $internalConfig += "mute-replay-warnings`r`n"
        $internalConfig += "replay-window 512 60`r`n"
        $internalConfig += "`r`n# Configuracoes de timeout para servidor`r`n"
        $internalConfig += "server-poll-timeout 30`r`n"
        $internalConfig += "ping 10`r`n"
        $internalConfig += "ping-restart 60`r`n"

        $internalConfig += "`r`n# Comentario explicativo`r`n"
        $internalConfig += "# Esta configuracao conecta a VPN apenas para obter IP interno`r`n"
        $internalConfig += "# O trafego de internet continua pela conexao normal do Windows`r`n"
        $internalConfig += "# Configuracoes anti-replay evitam erros ao recriar certificados`r`n"

        # Salvar conteudo corrigido + configuracoes adicionais
        Set-Content -Path $configPath -Value ($currentContent + $internalConfig) -Encoding UTF8

        Write-Host "[OK] Configuracao de VPN interna adicionada" -ForegroundColor Green
    }
}

# Funcao para validar arquivo .ovpn
function Test-OvpnFile {
    param([string]$configPath)

    Write-Host "[INFO] Validando arquivo .ovpn..." -ForegroundColor Yellow

    $content = Get-Content -Path $configPath -Raw
    $issues = @()

    # Verificar key-direction
    if ($content -match "key-direction 0") {
        $issues += "ERRO: key-direction deve ser 1 para cliente (encontrado: 0)"
    }

    # Verificar duplicatas
    $lines = Get-Content -Path $configPath
    $configCounts = @{}
    foreach ($line in $lines) {
        $trimmedLine = $line.Trim()
        if ($trimmedLine -match "^(resolv-retry|nobind|persist-key|persist-tun|route-nopull|mute-replay-warnings)") {
            $config = ($trimmedLine -split " ")[0]
            if ($configCounts.ContainsKey($config)) {
                $configCounts[$config]++
            } else {
                $configCounts[$config] = 1
            }
        }
    }

    foreach ($config in $configCounts.Keys) {
        if ($configCounts[$config] -gt 1) {
            $issues += "AVISO: Configuracao '$config' aparece $($configCounts[$config]) vezes"
        }
    }

    # Verificar configuracoes obrigatorias para VPN interna
    $requiredConfigs = @("route-nopull", "mute-replay-warnings", "replay-window")
    foreach ($config in $requiredConfigs) {
        if ($content -notmatch $config) {
            $issues += "ERRO: Configuracao obrigatoria '$config' nao encontrada"
        }
    }

    if ($issues.Count -eq 0) {
        Write-Host "[OK] Arquivo .ovpn validado com sucesso!" -ForegroundColor Green
        return $true
    } else {
        Write-Host "[PROBLEMAS ENCONTRADOS]" -ForegroundColor Red
        foreach ($issue in $issues) {
            Write-Host "  - $issue" -ForegroundColor Red
        }
        return $false
    }
}

# Funcao para limpar configuracoes duplicadas
function Remove-DuplicateConfigs {
    param([string]$configPath)

    Write-Host "[INFO] Limpando configuracoes duplicadas..." -ForegroundColor Yellow

    $content = Get-Content -Path $configPath
    $cleanedContent = @()
    $seenConfigs = @{}

    foreach ($line in $content) {
        $trimmedLine = $line.Trim()

        # Lista de configuracoes que nao devem ser duplicadas
        $configsToCheck = @(
            "resolv-retry", "nobind", "persist-key", "persist-tun",
            "route-nopull", "script-security", "connect-retry-max",
            "mute-replay-warnings", "replay-window", "verb",
            "server-poll-timeout", "ping", "ping-restart"
        )

        $isDuplicate = $false
        foreach ($config in $configsToCheck) {
            if ($trimmedLine -match "^$config") {
                if ($seenConfigs.ContainsKey($config)) {
                    $isDuplicate = $true
                    Write-Host "[REMOVIDO] Configuracao duplicada: $trimmedLine" -ForegroundColor Red
                    break
                } else {
                    $seenConfigs[$config] = $true
                }
            }
        }

        if (-not $isDuplicate) {
            $cleanedContent += $line
        }
    }

    # Salvar arquivo limpo
    Set-Content -Path $configPath -Value $cleanedContent -Encoding UTF8
    Write-Host "[OK] Configuracoes duplicadas removidas" -ForegroundColor Green
}

# Detectar versao do OpenVPN
$openVPNVersion = Detect-OpenVPNVersion

# Verificar configuracoes existentes
Check-ExistingVPNConfigs

# Testar conectividade avancada
if (-not (Test-VPNConnectivity)) {
    Write-Host "[ERRO] Problemas de conectividade detectados" -ForegroundColor Red
    Write-Host "[INFO] Verifique sua conexao de internet e tente novamente" -ForegroundColor Cyan
    Read-Host "Pressione ENTER para continuar mesmo assim ou Ctrl+C para cancelar"
}

# Definir caminho do arquivo
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
if (-not $scriptDir) { $scriptDir = Get-Location }
$configPath = Join-Path $scriptDir "$ClientName.ovpn"

Write-Host "[INFO] Arquivo sera salvo em: $configPath" -ForegroundColor Cyan

# Remover arquivo anterior
if (Test-Path $configPath) {
    Remove-Item $configPath -Force
    Write-Host "[INFO] Arquivo anterior removido" -ForegroundColor Cyan
}

# Verificar status do servidor antes de baixar certificado
Write-Host "[INFO] Verificando status do servidor OpenVPN..." -ForegroundColor Cyan
try {
    $serverCheck = Invoke-WebRequest -Uri "http://vpn.evo-eden.site:8080/api/health" -TimeoutSec 10 -UseBasicParsing
    Write-Host "[OK] Servidor OpenVPN respondendo" -ForegroundColor Green
} catch {
    Write-Host "[AVISO] Servidor pode estar com problemas" -ForegroundColor Yellow
    Write-Host "[SOLUCAO] Execute no servidor: sudo /root/VPN/OpenVPN/scripts/check-server.sh" -ForegroundColor Yellow
}

# Baixar certificado
Write-Host "[PROGRESSO] Baixando certificado..." -ForegroundColor Blue
try {
    $url = "http://$VPN_SERVER`:8080/generate/$ClientName/windows"
    Write-Host "[INFO] URL: $url" -ForegroundColor Cyan
    
    Invoke-WebRequest -Uri $url -OutFile $configPath -UseBasicParsing -TimeoutSec 30
    
    if (Test-Path $configPath) {
        $tamanho = (Get-Item $configPath).Length
        Write-Host "[OK] Arquivo baixado: $tamanho bytes" -ForegroundColor Green
        
        if ($tamanho -gt 0) {
            $content = Get-Content $configPath -Raw
            if ($content -match "<ca>") {
                Write-Host "[OK] Certificado valido!" -ForegroundColor Green

                # Aplicar correcoes de compatibilidade
                Fix-ConfigCompatibility $configPath $openVPNVersion

                # Limpar configuracoes duplicadas
                Remove-DuplicateConfigs $configPath

                # Validar arquivo final
                if (-not (Test-OvpnFile $configPath)) {
                    Write-Host "[ERRO] Arquivo .ovpn contem problemas!" -ForegroundColor Red
                    exit 1
                }

            } else {
                Write-Host "[ERRO] Arquivo sem certificados validos" -ForegroundColor Red
                exit 1
            }
        } else {
            Write-Host "[ERRO] Arquivo vazio" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "[ERRO] Arquivo nao foi criado" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "[ERRO] Falha no download: $($_.Exception.Message)" -ForegroundColor Red
    
    # Metodo alternativo
    Write-Host "[PROGRESSO] Tentando metodo alternativo..." -ForegroundColor Blue
    try {
        $webClient = New-Object System.Net.WebClient
        $webClient.Headers.Add("User-Agent", "PowerShell-VPN")
        $webClient.DownloadFile($url, $configPath)
        $webClient.Dispose()
        
        if (Test-Path $configPath -and (Get-Item $configPath).Length -gt 0) {
            Write-Host "[OK] Download alternativo funcionou!" -ForegroundColor Green
        } else {
            Write-Host "[ERRO] Metodo alternativo tambem falhou" -ForegroundColor Red
            exit 1
        }
    } catch {
        Write-Host "[ERRO] Todos os metodos falharam: $($_.Exception.Message)" -ForegroundColor Red
        
        # Instrucoes manuais
        Write-Host ""
        Write-Host "=== DOWNLOAD MANUAL ===" -ForegroundColor Yellow
        Write-Host "1. Abra seu navegador" -ForegroundColor White
        Write-Host "2. Acesse: $url" -ForegroundColor Cyan
        Write-Host "3. Salve o arquivo como: $configPath" -ForegroundColor Cyan
        Write-Host ""
        Read-Host "Pressione ENTER apos baixar o arquivo"
        
        if (-not (Test-Path $configPath)) {
            Write-Host "[ERRO] Arquivo nao encontrado apos download manual" -ForegroundColor Red
            exit 1
        }
    }
}

# Copiar para Downloads
Write-Host "[PROGRESSO] Criando backup..." -ForegroundColor Blue
try {
    $downloadsPath = "$env:USERPROFILE\Downloads\$ClientName.ovpn"
    Copy-Item $configPath $downloadsPath -Force
    Write-Host "[OK] Backup salvo em: $downloadsPath" -ForegroundColor Green
} catch {
    Write-Host "[AVISO] Nao foi possivel salvar backup em Downloads" -ForegroundColor Yellow
}

# Verificar OpenVPN
$openVPNPath = "${env:ProgramFiles}\OpenVPN\bin\openvpn.exe"
if (Test-Path $openVPNPath) {
    Write-Host "[OK] OpenVPN ja instalado" -ForegroundColor Green
    
    # Copiar para pasta de configuracao
    try {
        $openVPNConfigPath = "${env:ProgramFiles}\OpenVPN\config\$ClientName.ovpn"
        Copy-Item $configPath $openVPNConfigPath -Force
        Write-Host "[OK] Configuracao instalada no OpenVPN" -ForegroundColor Green
    } catch {
        Write-Host "[AVISO] Nao foi possivel copiar para pasta OpenVPN: $_" -ForegroundColor Yellow
    }
} else {
    Write-Host "[AVISO] OpenVPN nao encontrado" -ForegroundColor Yellow
    Write-Host "Baixe em: https://openvpn.net/community-downloads/" -ForegroundColor Cyan
}

# Resultado final
Write-Host ""
Write-Host "=== CONFIGURACAO VPN INTERNA CONCLUIDA ===" -ForegroundColor Green
Write-Host ""
Write-Host "CONFIGURACAO APLICADA:" -ForegroundColor Cyan
Write-Host "✓ VPN configurada para uso INTERNO apenas" -ForegroundColor Green
Write-Host "✓ Trafego de internet continua pela conexao normal" -ForegroundColor Green
Write-Host "✓ Windows agora tera IP na rede VPN interna" -ForegroundColor Green
Write-Host ""
Write-Host "ARQUIVOS CRIADOS:" -ForegroundColor Cyan
Write-Host "- Principal: $configPath" -ForegroundColor White
Write-Host "- Backup: $env:USERPROFILE\Downloads\$ClientName.ovpn" -ForegroundColor White
if (Test-Path $openVPNPath) {
    Write-Host "- OpenVPN: ${env:ProgramFiles}\OpenVPN\config\$ClientName.ovpn" -ForegroundColor White
}
Write-Host ""
Write-Host "IMPORTANTE - AGUARDE ANTES DE CONECTAR:" -ForegroundColor Red
Write-Host "Se este certificado foi recriado apos revogacao, aguarde 2-3 minutos" -ForegroundColor Yellow
Write-Host "antes da primeira conexao para evitar conflitos de replay." -ForegroundColor Yellow
Write-Host ""
Write-Host "COMO USAR:" -ForegroundColor Cyan
Write-Host "1. Instalar OpenVPN (se nao tiver): https://openvpn.net/community-downloads/" -ForegroundColor White
Write-Host "2. Executar OpenVPN GUI como Administrador" -ForegroundColor White
Write-Host "3. AGUARDE 2-3 minutos se certificado foi recriado" -ForegroundColor Red
Write-Host "4. Conectar ao perfil '$ClientName'" -ForegroundColor White
Write-Host "5. Verificar IP VPN: ipconfig (procurar interface TAP)" -ForegroundColor White
Write-Host ""
Write-Host "VERIFICACOES APOS CONECTAR:" -ForegroundColor Cyan
Write-Host "- Internet normal: ping ******* (deve funcionar pela conexao normal)" -ForegroundColor White
Write-Host "- Rede VPN interna: ping ********* (deve funcionar pela VPN)" -ForegroundColor White
Write-Host "- Verificar rotas: route print (nao deve ter 0.0.0.0 via VPN)" -ForegroundColor White
Write-Host ""
Write-Host "SUCESSO! Certificado VPN INTERNA criado e pronto para uso!" -ForegroundColor Green
Write-Host ""

# Verificar tamanho final
if (Test-Path $configPath) {
    $tamanhoFinal = (Get-Item $configPath).Length
    Write-Host "Tamanho do arquivo: $tamanhoFinal bytes" -ForegroundColor Gray
}

Write-Host "Pressione ENTER para sair..." -ForegroundColor Gray
Read-Host
