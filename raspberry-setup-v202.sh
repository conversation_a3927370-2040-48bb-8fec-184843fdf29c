#!/bin/bash

# Script de Configuração VPN para Raspberry Pi - VERSÃO VPN INTERNA
# Conecta à VPN apenas para obter IP interno, SEM redirecionar tráfego de internet
# Corrige o erro de data-ciphers em OpenVPN 2.4.x
# Autor: Sistema VPN EVO-EDEN
# Data: 2025-08-20
#
# IMPORTANTE: Esta configuração NÃO redireciona o tráfego de internet pela VPN
# A Raspberry mantém sua conexão normal de internet e apenas obtém um IP VPN interno

# Removido set -e para melhor controle de erros
# set -e

# Configurações
VPN_SERVER="${1:-vpn.evo-eden.site}"
CLIENT_NAME="$2"
VPN_PASSWORD="$3"
VPN_PORT="1194"
DOWNLOAD_URL="http://$VPN_SERVER:8080/download"
API_URL="http://$VPN_SERVER:8080/api"
REQUIRED_PASSWORD="VPNnbr5410!"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Funções de log
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" >&2
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" >&2
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" >&2
}

progress() {
    echo -e "${CYAN}[PROGRESS]${NC} $1" >&2
}

# Detectar versão do OpenVPN
detect_openvpn_version() {
    local version_output=$(openvpn --version 2>&1 | head -1)
    local version=$(echo "$version_output" | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1)

    if [ -n "$version" ]; then
        log "OpenVPN versão detectada: $version"

        # Extrair versão principal (ex: 2.4 de 2.4.7)
        local major_minor=$(echo "$version" | cut -d. -f1-2)

        # Verificar se é versão 2.4 ou anterior (não suporta data-ciphers)
        if [ "$major_minor" = "2.4" ] || [ "$major_minor" = "2.3" ] || [ "$major_minor" = "2.2" ]; then
            warning "OpenVPN $version - usando configuração compatível (sem data-ciphers)"
            # Retornar apenas o resultado para stdout
            echo "legacy"
        else
            log "OpenVPN $version - suporte completo a configurações modernas"
            # Retornar apenas o resultado para stdout
            echo "modern"
        fi
    else
        warning "Não foi possível detectar versão do OpenVPN - assumindo versão legacy"
        # Retornar apenas o resultado para stdout
        echo "legacy"
    fi
}

# Verificar senha de acesso
check_password() {
    if [ -z "$VPN_PASSWORD" ]; then
        echo -n "Digite a senha de acesso: "
        read -s VPN_PASSWORD
        echo
    fi

    if [ "$VPN_PASSWORD" != "$REQUIRED_PASSWORD" ]; then
        error "Senha incorreta!"
        exit 1
    fi

    log "Senha verificada com sucesso"
}

# Verificar se está executando como root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        error "Este script deve ser executado como root"
        info "Execute: sudo $0 $*"
        exit 1
    fi
    log "Executando como root"
}

# Verificar configurações VPN existentes
check_existing_vpn() {
    local existing_configs=()
    local existing_services=()

    # Verificar arquivos de configuração
    if [ -d "/etc/openvpn/client" ]; then
        while IFS= read -r -d '' config_file; do
            existing_configs+=("$(basename "$config_file")")
        done < <(find /etc/openvpn/client -name "*.conf" -print0 2>/dev/null)
    fi

    # Verificar serviços ativos
    while IFS= read -r service; do
        if [ -n "$service" ]; then
            existing_services+=("$service")
        fi
    done < <(systemctl list-units --type=service --state=active | grep "openvpn-client@" | awk '{print $1}' 2>/dev/null)

    # Se há configurações existentes, perguntar ao usuário
    if [ ${#existing_configs[@]} -gt 0 ] || [ ${#existing_services[@]} -gt 0 ]; then
        warning "Configurações VPN existentes encontradas:"

        if [ ${#existing_configs[@]} -gt 0 ]; then
            info "Arquivos de configuração:"
            for config in "${existing_configs[@]}"; do
                info "  • $config"
            done
        fi

        if [ ${#existing_services[@]} -gt 0 ]; then
            info "Serviços ativos:"
            for service in "${existing_services[@]}"; do
                info "  • $service"
            done
        fi

        echo
        echo -n "Deseja remover TODAS as configurações VPN existentes? [s/N]: "
        read -r response

        if [[ "$response" =~ ^[Ss]$ ]]; then
            clean_existing_vpn_force
        else
            warning "Mantendo configurações existentes. Isso pode causar conflitos."
            echo -n "Continuar mesmo assim? [s/N]: "
            read -r continue_response
            if [[ ! "$continue_response" =~ ^[Ss]$ ]]; then
                info "Operação cancelada pelo usuário."
                exit 0
            fi
        fi
    else
        log "Nenhuma configuração VPN existente encontrada"
    fi
}

# Limpar configurações VPN existentes (forçado)
clean_existing_vpn_force() {
    progress "Removendo configurações VPN da rede 10.12.0.X (EVO-EDEN)..."

    # Identificar e parar apenas serviços VPN relacionados à nossa rede
    info "Verificando serviços OpenVPN client da nossa rede..."
    for service in $(systemctl list-units --type=service --state=active | grep "openvpn-client@" | awk '{print $1}' 2>/dev/null); do
        if [ -n "$service" ]; then
            # Extrair nome do cliente do serviço
            local client_name=$(echo "$service" | sed 's/openvpn-client@\(.*\)\.service/\1/')
            local config_file="/etc/openvpn/client/${client_name}.conf"

            # Verificar se a configuração é da nossa rede (10.12.0.X)
            if [ -f "$config_file" ] && grep -q "10\.12\.0\." "$config_file" 2>/dev/null; then
                info "Parando serviço da nossa VPN: $service"
                systemctl stop "$service" 2>/dev/null || true
                systemctl disable "$service" 2>/dev/null || true

                # Remover symlink específico
                rm -f "/etc/systemd/system/multi-user.target.wants/$service" 2>/dev/null || true

                # Remover arquivo de configuração específico
                info "Removendo configuração: $config_file"
                rm -f "$config_file" 2>/dev/null || true
            else
                info "Mantendo serviço de outra VPN: $service"
            fi
        fi
    done

    # Remover apenas arquivos temporários da nossa VPN
    rm -f /tmp/*evo-eden*.ovpn 2>/dev/null || true
    rm -f /tmp/*10.12.0*.ovpn 2>/dev/null || true

    # Remover apenas interfaces tun da rede VPN 10.12.0.X (nossa VPN)
    info "Verificando interfaces TUN da rede 10.12.0.X..."
    for tun_if in $(ip addr show | grep "tun[0-9]" | awk '{print $2}' | cut -d: -f1 2>/dev/null); do
        if [ -n "$tun_if" ]; then
            # Verificar se a interface tem IP da nossa rede VPN (10.12.0.X)
            local tun_ip=$(ip addr show "$tun_if" | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1 2>/dev/null)
            if [ -n "$tun_ip" ] && echo "$tun_ip" | grep -q "^10\.12\.0\."; then
                info "Removendo interface da nossa VPN: $tun_if ($tun_ip)"
                ip link delete "$tun_if" 2>/dev/null || true
            else
                info "Mantendo interface de outra VPN: $tun_if ($tun_ip)"
            fi
        fi
    done

    # Recarregar systemd
    systemctl daemon-reload

    log "✅ Todas as configurações VPN foram removidas"
}

# Verificar módulo TUN
check_tun_module() {
    progress "Verificando suporte TUN/TAP..."
    
    # Carregar módulo TUN se necessário
    if ! lsmod | grep -q "^tun "; then
        info "Carregando módulo TUN..."
        modprobe tun 2>/dev/null || true
    fi
    
    # Verificar dispositivo /dev/net/tun
    if [ ! -c /dev/net/tun ]; then
        info "Criando dispositivo /dev/net/tun..."
        mkdir -p /dev/net 2>/dev/null || true
        mknod /dev/net/tun c 10 200 2>/dev/null || true
    fi
    
    chmod 666 /dev/net/tun 2>/dev/null || true
    log "Módulo TUN configurado"
}

# Instalar dependências
install_dependencies() {
    progress "Instalando dependências..."
    apt-get update -qq
    apt-get install -y openvpn curl wget netcat-openbsd
    log "Dependências instaladas"
}

# Baixar certificado
get_vpn_certificate() {
    local client_name="$1"
    local config_path="/tmp/$client_name.ovpn"

    progress "Baixando certificado VPN..."

    # Verificar conectividade com servidor VPN primeiro
    info "Verificando conectividade com servidor VPN..."

    # Testar resolução DNS com múltiplos métodos (versão ultra-robusta)
    info "Testando resolução DNS para vpn.evo-eden.site..."
    local dns_resolved=false
    local resolved_ip=""
    local dns_method=""

    # Debug: mostrar configuração DNS
    info "Configuração DNS do sistema:"
    if [ -f "/etc/resolv.conf" ]; then
        local nameservers=$(grep "^nameserver" /etc/resolv.conf | awk '{print $2}' | tr '\n' ' ')
        info "  • Nameservers: $nameservers"
    fi

    # Método 1: getent hosts (mais universal) - versão defensiva
    info "Tentando método 1: getent hosts..."
    local getent_output=""
    if command -v getent >/dev/null 2>&1; then
        getent_output=$(getent hosts vpn.evo-eden.site 2>/dev/null || true)
        info "  • Saída do getent: '$getent_output'"

        if [ -n "$getent_output" ]; then
            # Extrair IP de forma mais robusta
            resolved_ip=$(echo "$getent_output" | awk '{print $1}' | head -1 | tr -d ' \t\n\r')
            info "  • IP extraído: '$resolved_ip'"

            # Validar se é um IP válido
            if [[ "$resolved_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                dns_resolved=true
                dns_method="getent"
                info "✅ DNS resolvido via getent: $resolved_ip"
            else
                warning "  • IP inválido extraído do getent: '$resolved_ip'"
            fi
        else
            warning "  • getent não retornou resultado"
        fi
    else
        warning "  • Comando getent não disponível"
    fi

    # Método 2: host (se getent falhar)
    if [ "$dns_resolved" = false ]; then
        info "Tentando método 2: host..."
        if command -v host >/dev/null 2>&1; then
            local host_output=""
            host_output=$(host vpn.evo-eden.site 2>/dev/null || true)
            info "  • Saída do host: '$host_output'"

            if echo "$host_output" | grep -q "has address"; then
                resolved_ip=$(echo "$host_output" | grep "has address" | awk '{print $4}' | head -1 | tr -d ' \t\n\r')
                info "  • IP extraído: '$resolved_ip'"

                if [[ "$resolved_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                    dns_resolved=true
                    dns_method="host"
                    info "✅ DNS resolvido via host: $resolved_ip"
                else
                    warning "  • IP inválido extraído do host: '$resolved_ip'"
                fi
            else
                warning "  • host não retornou endereço válido"
            fi
        else
            warning "  • Comando host não disponível"
        fi
    fi

    # Método 3: dig (se host falhar)
    if [ "$dns_resolved" = false ]; then
        info "Tentando método 3: dig..."
        if command -v dig >/dev/null 2>&1; then
            local dig_output=""
            dig_output=$(dig +short vpn.evo-eden.site 2>/dev/null | head -1 | tr -d ' \t\n\r' || true)
            info "  • Saída do dig: '$dig_output'"

            if [ -n "$dig_output" ] && [[ "$dig_output" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                resolved_ip="$dig_output"
                dns_resolved=true
                dns_method="dig"
                info "✅ DNS resolvido via dig: $resolved_ip"
            else
                warning "  • dig não retornou IP válido: '$dig_output'"
            fi
        else
            warning "  • Comando dig não disponível"
        fi
    fi

    # Método 4: nslookup (último recurso)
    if [ "$dns_resolved" = false ]; then
        info "Tentando método 4: nslookup..."
        if command -v nslookup >/dev/null 2>&1; then
            local nslookup_output=""
            nslookup_output=$(nslookup vpn.evo-eden.site 2>/dev/null || true)
            info "  • Saída do nslookup: '$nslookup_output'"

            if echo "$nslookup_output" | grep -q "Address:"; then
                resolved_ip=$(echo "$nslookup_output" | grep "Address:" | tail -1 | awk '{print $2}' | tr -d ' \t\n\r')
                info "  • IP extraído: '$resolved_ip'"

                if [[ "$resolved_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                    dns_resolved=true
                    dns_method="nslookup"
                    info "✅ DNS resolvido via nslookup: $resolved_ip"
                else
                    warning "  • IP inválido extraído do nslookup: '$resolved_ip'"
                fi
            else
                warning "  • nslookup não retornou endereço válido"
            fi
        else
            warning "  • Comando nslookup não disponível"
        fi
    fi

    # Verificar se conseguiu resolver
    if [ "$dns_resolved" = false ]; then
        error "❌ Não foi possível resolver DNS para vpn.evo-eden.site"
        warning "Métodos testados: getent, host, dig, nslookup"
        warning "Configuração DNS atual:"
        [ -f "/etc/resolv.conf" ] && cat /etc/resolv.conf | sed 's/^/    /'

        # Último recurso: tentar ping direto (pode funcionar mesmo com DNS falhando)
        warning "Tentando último recurso: ping direto..."
        if ping -c 1 -W 5 vpn.evo-eden.site >/dev/null 2>&1; then
            warning "⚠️ Ping funciona mas DNS falhou - problema de resolução DNS"
            warning "Continuando mesmo assim, pois servidor está acessível"
            resolved_ip="vpn.evo-eden.site"  # Usar hostname diretamente
            dns_method="ping-fallback"
            dns_resolved=true
        else
            warning "Teste manual sugerido: getent hosts vpn.evo-eden.site"
            return 1
        fi
    fi

    # Testar conectividade básica (ping) - mais robusto
    info "Testando conectividade básica (ping)..."
    local ping_success=false

    # Tentar ping com diferentes configurações
    for attempt in 1 2 3; do
        info "Tentativa $attempt de ping..."
        if ping -c 2 -W 5 vpn.evo-eden.site >/dev/null 2>&1; then
            ping_success=true
            log "✅ Ping para servidor bem-sucedido"
            break
        fi

        if [ $attempt -lt 3 ]; then
            warning "Ping falhou, tentando novamente em 2 segundos..."
            sleep 2
        fi
    done

    if [ "$ping_success" = false ]; then
        error "❌ Servidor VPN não está acessível (ping falhou após 3 tentativas)"
        warning "Verifique conectividade de rede"
        warning "Teste manual: ping vpn.evo-eden.site"
        return 1
    fi

    # Testar conectividade com API VPN
    info "Testando conectividade com API VPN..."
    local api_success=false

    # Testar API com timeout mais generoso
    local test_response=$(curl -s --connect-timeout 15 --max-time 30 "http://vpn.evo-eden.site:8080/generate/TEST-CONNECTIVITY/linux" 2>/dev/null)

    if [ -n "$test_response" ] && echo "$test_response" | grep -q "client"; then
        api_success=true
        log "✅ API VPN respondendo corretamente"
    else
        warning "⚠️ API VPN não respondeu adequadamente"

        # Testar conectividade básica na porta 8080
        info "Testando conectividade na porta 8080..."
        if nc -z vpn.evo-eden.site 8080 2>/dev/null; then
            warning "Porta 8080 acessível, mas API não responde corretamente"
        else
            warning "Porta 8080 não acessível"
        fi

        warning "Continuando mesmo assim - pode ser problema temporário"
    fi

    log "✅ Conectividade básica com servidor OK"

    # Informações de debug
    info "Informações de conectividade:"
    info "  • Servidor: vpn.evo-eden.site"
    info "  • IP resolvido: $resolved_ip (via $dns_method)"
    info "  • Ping: OK"
    info "  • API: $([ "$api_success" = true ] && echo "OK" || echo "Problema detectado")"

    # Teste adicional: verificar se conseguimos fazer uma conexão UDP básica
    info "Testando conectividade UDP na porta 1194..."
    if timeout 5 bash -c "echo 'test' | nc -u vpn.evo-eden.site 1194" >/dev/null 2>&1; then
        log "✅ Conectividade UDP na porta 1194 OK"
    else
        warning "⚠️ Teste UDP na porta 1194 falhou (pode ser normal para OpenVPN)"
        info "Continuando com download do certificado..."
    fi

    # Remover arquivo anterior se existir
    [ -f "$config_path" ] && rm -f "$config_path"

    # Tentar baixar via API com múltiplas tentativas
    local api_generate_url="http://$VPN_SERVER:8080/generate/$client_name/linux"
    info "URL: $api_generate_url"

    # Múltiplas tentativas de download
    for attempt in 1 2 3; do
        info "Tentativa $attempt de download..."

        if curl -s -o "$config_path" "$api_generate_url" --connect-timeout 30 --max-time 60; then
            if [ -s "$config_path" ] && grep -q "<ca>" "$config_path"; then
                local size=$(wc -c < "$config_path")
                log "✅ Certificado baixado: $config_path ($size bytes)"
                # Retornar apenas o caminho do arquivo para stdout
                echo "$config_path"
                return 0
            else
                warning "Arquivo baixado mas inválido (tentativa $attempt)"
                [ -f "$config_path" ] && rm -f "$config_path"
            fi
        else
            warning "Falha no download (tentativa $attempt)"
        fi

        if [ $attempt -lt 3 ]; then
            warning "Aguardando 5 segundos antes da próxima tentativa..."
            sleep 5
        fi
    done

    error "Falha ao baixar certificado"
    return 1
}

# Corrigir configuração para compatibilidade com OpenVPN 2.4.x
fix_config_compatibility() {
    local config_file="$1"
    local openvpn_compat=$(detect_openvpn_version)
    
    info "Aplicando correções de compatibilidade..."
    
    if [ "$openvpn_compat" = "legacy" ]; then
        # Remover opções incompatíveis com OpenVPN 2.4.x
        sed -i '/^data-ciphers/d' "$config_file"
        sed -i '/^data-ciphers-fallback/d' "$config_file"
        sed -i '/^tls-ciphersuites/d' "$config_file"
        
        # Garantir que há configuração de cipher compatível
        if ! grep -q "^cipher" "$config_file"; then
            echo "" >> "$config_file"
            echo "# Configuração compatível com OpenVPN 2.4.x" >> "$config_file"
            echo "cipher AES-256-GCM" >> "$config_file"
            echo "auth SHA256" >> "$config_file"
        fi
        
        log "Configuração corrigida para OpenVPN 2.4.x"
    fi
}

# Instalar configuração VPN
install_vpn_config() {
    local config_path="$1"
    local client_name="$2"

    local target_path="/etc/openvpn/client/$client_name.conf"

    progress "Instalando configuração VPN..."

    # Verificar se arquivo de origem existe
    if [ ! -f "$config_path" ]; then
        error "Arquivo de configuração não encontrado: $config_path"
        return 1
    fi

    # Criar diretório se não existir
    mkdir -p "/etc/openvpn/client"

    # Copiar configuração
    info "Copiando $config_path para $target_path"
    if ! cp "$config_path" "$target_path"; then
        error "Falha ao copiar configuração de $config_path para $target_path"
        ls -la "$config_path" >&2
        ls -la "$(dirname "$target_path")" >&2
        return 1
    fi

    # Verificar se cópia foi bem-sucedida
    if [ ! -f "$target_path" ]; then
        error "Arquivo não foi criado após cópia: $target_path"
        return 1
    fi

    info "Arquivo copiado com sucesso: $(wc -c < "$target_path") bytes"

    # Aplicar correções de compatibilidade
    fix_config_compatibility "$target_path"

    # Verificar se arquivo ainda existe após correções
    if [ ! -f "$target_path" ]; then
        error "Arquivo de configuração foi removido durante correções: $target_path"
        return 1
    fi

    info "Correções aplicadas. Tamanho final: $(wc -c < "$target_path") bytes"

    # Adicionar configurações específicas para Raspberry Pi (SEM redirecionamento de tráfego)
    cat >> "$target_path" << EOF

# Configurações específicas para Raspberry Pi
# IMPORTANTE: Configuração para VPN interna apenas (sem redirecionamento de internet)
script-security 2
dev-type tun
remote-cert-tls server
connect-timeout 30
connect-retry 5
resolv-retry infinite
nobind
persist-key
persist-tun

# REMOVIDO: pull e redirect-gateway para evitar redirecionamento de tráfego
# pull
# redirect-gateway def1
# dhcp-option DNS *******
# dhcp-option DNS *******

# === CONFIGURAÇÕES VPN INTERNA OBRIGATÓRIAS ===
# IMPORTANTE: Configuração para VPN interna apenas (sem redirecionamento de internet)
route-nopull
connect-timeout 30
connect-retry 5
connect-retry-max 3
resolv-retry infinite
nobind
persist-key
persist-tun
verb 3

# Configurações anti-replay (evita conflitos de timestamp)
mute-replay-warnings
replay-window 512 60

# Configurações de timeout para servidor
server-poll-timeout 30
ping 10
ping-restart 60

# Comentário explicativo
# Esta configuração conecta à VPN apenas para obter IP interno
# O tráfego de internet continua pela conexão normal da Raspberry
# Configurações anti-replay evitam erros ao recriar certificados
EOF
    
    # Ajustar permissões
    chmod 600 "$target_path"
    
    log "Configuração instalada: $target_path"
    
    # Habilitar serviço
    systemctl daemon-reload
    systemctl enable openvpn-client@"$client_name"
    
    log "Serviço OpenVPN habilitado para $client_name"
}

# Testar configuração
test_vpn_config() {
    local client_name="$1"
    local config_file="/etc/openvpn/client/$client_name.conf"
    
    progress "Testando configuração VPN..."

    # Verificar se arquivo de configuração existe
    if [ ! -f "$config_file" ]; then
        error "Arquivo de configuração não encontrado: $config_file"
        return 1
    fi

    # Testar sintaxe (método mais simples para OpenVPN 2.4.x)
    local syntax_output=$(openvpn --config "$config_file" --verb 1 --show-ciphers 2>&1 | head -1)

    # Se conseguir ler a configuração sem erro, a sintaxe está OK
    if openvpn --config "$config_file" --verb 0 --show-ciphers >/dev/null 2>&1; then
        log "Sintaxe da configuração válida"
    else
        # Tentar método alternativo
        local alt_output=$(openvpn --config "$config_file" 2>&1 | head -5)
        if echo "$alt_output" | grep -q "Options error\|Unrecognized option"; then
            error "Erro de sintaxe na configuração:"
            echo "$alt_output" | head -5 | sed 's/^/  /' >&2
            return 1
        else
            warning "Não foi possível validar sintaxe, mas configuração parece OK"
        fi
    fi

    # Iniciar serviço
    info "Iniciando serviço OpenVPN client..."
    if ! systemctl start "openvpn-client@$client_name"; then
        error "Falha ao iniciar serviço OpenVPN"
        return 1
    fi

    # Aguardar um pouco para o serviço inicializar
    sleep 3

    # Verificar se serviço está ativo
    if ! systemctl is-active "openvpn-client@$client_name" >/dev/null; then
        error "Serviço OpenVPN não está ativo"
        warning "Verificando logs do serviço..."
        journalctl -u "openvpn-client@$client_name" --no-pager -n 10 | sed 's/^/  /' >&2
        return 1
    fi

    log "Serviço OpenVPN iniciado e ativo"

    # Aguardar interface aparecer
    local timeout=45
    local count=0

    info "Aguardando criação da interface VPN (timeout: ${timeout}s)..."

    while [ $count -lt $timeout ]; do
        sleep 1
        count=$((count + 1))

        # Mostrar progresso a cada 5 segundos
        if [ $((count % 5)) -eq 0 ]; then
            info "Aguardando... ${count}/${timeout}s"
        fi

        # Verificar interface TUN
        if ip addr show | grep -q "tun[0-9]"; then
            local tun_interface=$(ip addr show | grep "tun[0-9]" | head -1 | awk '{print $2}' | cut -d: -f1)
            local vpn_ip=$(ip addr show "$tun_interface" | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1 2>/dev/null)
            log "✅ VPN conectada! Interface: $tun_interface ($vpn_ip)"

            # Testar conectividade VPN interna (ping para gateway VPN)
            local vpn_gateway=$(ip route show dev "$tun_interface" | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1)
            if [ -n "$vpn_gateway" ] && ping -c 1 -W 3 "$vpn_gateway" >/dev/null 2>&1; then
                log "✅ Conectividade VPN interna funcionando (gateway: $vpn_gateway)"
            else
                # Tentar ping para rede VPN (*********)
                if ping -c 1 -W 3 ********* >/dev/null 2>&1; then
                    log "✅ Conectividade VPN interna funcionando (servidor: *********)"
                else
                    warning "Interface VPN criada mas sem conectividade interna"
                fi
            fi

            # Verificar se internet continua funcionando pela conexão normal
            info "Verificando se internet continua funcionando pela conexão normal..."
            if ping -c 1 -W 3 ******* >/dev/null 2>&1; then
                log "✅ Internet funcionando pela conexão normal (correto!)"
            else
                warning "⚠️ Problema de conectividade de internet"
            fi

            return 0
        fi

        # Verificar se serviço ainda está ativo
        if ! systemctl is-active "openvpn-client@$client_name" >/dev/null; then
            error "Serviço OpenVPN parou de funcionar durante teste"
            warning "Logs do serviço:"
            journalctl -u "openvpn-client@$client_name" --no-pager -n 10 | sed 's/^/  /' >&2

            # Verificar se há erros específicos nos logs
            local recent_logs=$(journalctl -u "openvpn-client@$client_name" --no-pager -n 20 2>/dev/null)

            if echo "$recent_logs" | grep -q "AUTH_FAILED"; then
                error "🔍 PROBLEMA: Falha de autenticação - certificado pode estar revogado"
            elif echo "$recent_logs" | grep -q "cannot locate HMAC"; then
                error "🔍 PROBLEMA: TLS Error - incompatibilidade de configuração HMAC/tls-auth"
                warning "SOLUÇÃO: Servidor precisa ter tls-auth configurado ou cliente sem tls-auth"
                warning "Contate administrador para corrigir configuração do servidor"
            elif echo "$recent_logs" | grep -q "TLS Error"; then
                error "🔍 PROBLEMA: Erro TLS - problema com certificados ou configuração"
            elif echo "$recent_logs" | grep -q "Connection refused"; then
                error "🔍 PROBLEMA: Conexão recusada - servidor pode estar offline"
            elif echo "$recent_logs" | grep -q "RESOLVE"; then
                error "🔍 PROBLEMA: Erro de DNS - não consegue resolver servidor"
            elif echo "$recent_logs" | grep -q "timeout"; then
                error "🔍 PROBLEMA: Timeout - servidor não responde"
            fi

            return 1
        fi
    done

    warning "Interface VPN não criada após $timeout segundos"
    warning "Verificando status do serviço..."
    systemctl status "openvpn-client@$client_name" --no-pager | sed 's/^/  /' >&2
    warning "Últimos logs:"
    journalctl -u "openvpn-client@$client_name" --no-pager -n 15 | sed 's/^/  /' >&2

    # Diagnóstico adicional
    warning "Diagnóstico adicional:"
    info "  • Verificando conectividade com servidor..."
    if ping -c 1 -W 3 vpn.evo-eden.site >/dev/null 2>&1; then
        info "    ✅ Ping para servidor OK"
    else
        error "    ❌ Ping para servidor falhou"
    fi

    info "  • Verificando resolução DNS..."
    local resolved_ip=$(nslookup vpn.evo-eden.site | grep "Address:" | tail -1 | awk '{print $2}' 2>/dev/null)
    if [ -n "$resolved_ip" ]; then
        info "    ✅ DNS resolve para: $resolved_ip"
    else
        error "    ❌ Falha na resolução DNS"
    fi

    info "  • Verificando arquivo de configuração..."
    if [ -f "/etc/openvpn/client/$client_name.conf" ]; then
        local config_size=$(wc -c < "/etc/openvpn/client/$client_name.conf")
        info "    ✅ Arquivo config existe ($config_size bytes)"

        if grep -q "<ca>" "/etc/openvpn/client/$client_name.conf"; then
            info "    ✅ Certificados presentes no arquivo"
        else
            error "    ❌ Certificados não encontrados no arquivo"
        fi
    else
        error "    ❌ Arquivo de configuração não existe"
    fi

    # Verificar se é problema específico de HMAC/tls-auth
    if journalctl -u "openvpn-client@$client_name" --no-pager -n 20 2>/dev/null | grep -q "cannot locate HMAC"; then
        error "🎯 PROBLEMA ESPECÍFICO IDENTIFICADO: Incompatibilidade TLS-AUTH"
        warning "O servidor não está configurado com tls-auth mas o certificado inclui"
        warning "SOLUÇÃO IMEDIATA: Contate o administrador para adicionar tls-auth no servidor"
        warning "COMANDO PARA ADMIN: Adicionar 'tls-auth /caminho/ta.key 0' na configuração do servidor"
    fi

    warning "POSSÍVEIS CAUSAS E SOLUÇÕES:"
    warning "  1. Incompatibilidade TLS-AUTH → Servidor precisa de tls-auth configurado"
    warning "  2. Certificado revogado → Gere novo certificado com nome diferente"
    warning "  3. Servidor VPN offline → Verifique se servidor está rodando"
    warning "  4. Firewall bloqueando → Verifique regras de firewall"
    warning "  5. Problema de rede → Teste: ping vpn.evo-eden.site"
    warning "  6. OpenVPN 2.4.x incompatível → Configuração já ajustada"
    warning "  7. Problema de DNS → Teste: nslookup vpn.evo-eden.site"

    warning "COMANDOS PARA DIAGNÓSTICO:"
    warning "  • journalctl -u openvpn-client@$client_name -f"
    warning "  • systemctl status openvpn-client@$client_name"
    warning "  • ping vpn.evo-eden.site"
    warning "  • nslookup vpn.evo-eden.site"

    return 1
}

# Função principal
main() {
    log "=== CONFIGURAÇÃO VPN PARA RASPBERRY PI - VPN INTERNA ==="
    log "IMPORTANTE: Esta configuração NÃO redireciona tráfego de internet"
    log "A VPN será usada apenas para obter IP interno na rede VPN"
    log "Servidor: $VPN_SERVER"
    log "Cliente: $CLIENT_NAME"
    echo
    
    # Verificações iniciais
    check_password
    check_root
    
    # Detectar versão do OpenVPN
    local openvpn_version_info=$(detect_openvpn_version)
    
    # Verificação e limpeza de configurações existentes
    check_existing_vpn
    check_tun_module
    install_dependencies
    
    # Obter e instalar certificado
    config_path=$(get_vpn_certificate "$CLIENT_NAME")
    if [ -z "$config_path" ]; then
        error "Falha ao obter certificado VPN"
        exit 1
    fi
    
    if ! install_vpn_config "$config_path" "$CLIENT_NAME"; then
        error "Falha na instalação da configuração VPN"
        exit 1
    fi

    # Testar conexão
    if test_vpn_config "$CLIENT_NAME"; then
        log "=== CONFIGURAÇÃO VPN INTERNA CONCLUÍDA COM SUCESSO! ==="
        echo
        log "✅ VPN configurada para uso INTERNO apenas"
        log "✅ Tráfego de internet continua pela conexão normal"
        log "✅ Raspberry agora tem IP na rede VPN interna"
        log "✅ Configurações anti-replay aplicadas"
        echo
        warning "IMPORTANTE: Se este certificado foi recriado após revogação,"
        warning "aguarde 2-3 minutos antes de reconectar para evitar conflitos de replay."
        echo
        log "COMANDOS ÚTEIS:"
        info "- Status: systemctl status openvpn-client@$CLIENT_NAME"
        info "- Logs: journalctl -u openvpn-client@$CLIENT_NAME -f"
        info "- Ver IP VPN: ip addr show | grep tun"
        info "- Parar: systemctl stop openvpn-client@$CLIENT_NAME"
        info "- Iniciar: systemctl start openvpn-client@$CLIENT_NAME"
        echo
        log "VERIFICAÇÕES APÓS CONECTAR:"
        info "- Internet normal: ping ******* (deve funcionar pela conexão normal)"
        info "- Rede VPN interna: ping ********* (deve funcionar pela VPN)"
        info "- Verificar rotas: ip route (não deve ter 0.0.0.0 via VPN)"
    else
        warning "Configuração instalada, mas teste de conexão falhou"
        info "Verifique os logs: journalctl -u openvpn-client@$CLIENT_NAME"
    fi
}

# Mostrar instruções se parâmetros estiverem faltando
if [ -z "$CLIENT_NAME" ]; then
    echo "Script de Configuração VPN para Raspberry Pi - VPN INTERNA"
    echo "IMPORTANTE: Esta configuração NÃO redireciona tráfego de internet"
    echo "A VPN será usada apenas para obter IP interno na rede VPN"
    echo ""
    echo "Uso: $0 [servidor] [nome-cliente] [senha]"
    echo ""
    echo "Exemplo:"
    echo "  $0 vpn.evo-eden.site ETEN-ANJO-DA-GUARDA-MAURITI [senha-fornecida]"
    echo ""
    echo "Ou execute sem parâmetros para modo interativo:"
    echo "  $0"
    echo ""
    
    read -p "Digite o nome do cliente (ex: ETEN-ANJO-DA-GUARDA-MAURITI): " CLIENT_NAME
    if [ -z "$CLIENT_NAME" ]; then
        error "Nome do cliente é obrigatório"
        exit 1
    fi
fi

# Validar nome do cliente
if ! echo "$CLIENT_NAME" | grep -q '^[a-zA-Z0-9_-]\+$'; then
    error "Nome do cliente deve conter apenas letras, números, _ e -"
    exit 1
fi

# Manter nome original do cliente (sem timestamp)
ORIGINAL_CLIENT_NAME="$CLIENT_NAME"
info "Usando nome do cliente: $CLIENT_NAME"

# Executar configuração
main
